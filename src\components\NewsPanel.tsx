import { Card, Typography, Spin, message } from 'antd'
import { LeftOutlined, RightOutlined } from '@ant-design/icons'
import { motion } from 'framer-motion'
import { useState, useEffect, useCallback } from 'react'
import { NewsItem } from '../types/news'
import { fetchRandomNews } from '../services/newsApi'
import './NewsPanel.css'

export const NewsPanel: React.FC = () => {
  const [newsItems, setNewsItems] = useState<NewsItem[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [currentIndex, setCurrentIndex] = useState<number>(0)
  const [error, setError] = useState<string | null>(null)
  const [isAutoPlaying, setIsAutoPlaying] = useState<boolean>(true)
  const [autoPlayInterval, setAutoPlayInterval] = useState<NodeJS.Timeout | null>(null)

  // 获取新闻数据
  const loadNews = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const news = await fetchRandomNews(6) // 获取6条新闻用于轮播
      setNewsItems(news)
    } catch (err) {
      console.error('Failed to load news:', err)
      setError('Failed to load news')
      message.error('Failed to load news')
    } finally {
      setLoading(false)
    }
  }, [])

  // 组件挂载时加载数据
  useEffect(() => {
    loadNews()
  }, [loadNews])

  // 自动轮播逻辑
  const stopAutoPlay = useCallback(() => {
    if (autoPlayInterval) {
      clearInterval(autoPlayInterval)
      setAutoPlayInterval(null)
    }
  }, [autoPlayInterval])

  // 当新闻数据加载完成且开启自动播放时，启动自动轮播
  useEffect(() => {
    if (newsItems.length > 0 && isAutoPlaying && !autoPlayInterval) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => {
          const nextIndex = prev === newsItems.length - 1 ? 0 : prev + 1
          return nextIndex
        })
      }, 4000) // 每4秒切换一次

      setAutoPlayInterval(interval)

      return () => {
        clearInterval(interval)
      }
    }
  }, [newsItems.length, isAutoPlaying])

  // 轮播控制
  const goToPrevious = useCallback(() => {
    stopAutoPlay() // 手动操作时停止自动播放
    setCurrentIndex((prev) => (prev === 0 ? newsItems.length - 1 : prev - 1))
    setIsAutoPlaying(false)
  }, [newsItems.length, stopAutoPlay])

  const goToNext = useCallback(() => {
    stopAutoPlay() // 手动操作时停止自动播放
    setCurrentIndex((prev) => (prev === newsItems.length - 1 ? 0 : prev + 1))
    setIsAutoPlaying(false)
  }, [newsItems.length, stopAutoPlay])

  // 获取当前显示的新闻（展示 4 条用于轮播 + Grid 布局）
  const getCurrentNews = useCallback(() => {
    if (newsItems.length === 0) return []
    const news: NewsItem[] = []
    for (let i = 0; i < 4; i++) {
      const index = (currentIndex + i) % newsItems.length
      news.push(newsItems[index])
    }
    return news
  }, [newsItems, currentIndex])

  const currentNews = getCurrentNews()

  // 渲染新闻卡片
  const renderNewsCard = (news: NewsItem, index: number) => (
    <motion.div key={`${news.id}-${currentIndex}`} initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.2 }} whileHover={{ y: -5 }}>
      <Card
        className="news-card"
        style={{
          width: '100%',
          minHeight: '280px',
          background: 'rgba(255, 255, 255, 0.08)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.15)',
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
          transition: 'all 0.3s ease',
        }}
        styles={{ body: { padding: '24px' } }}
      >
        {/* News Icon Badge */}
        <div
          style={{
            width: '100%',
            height: '80px',
            marginBottom: '16px',
            borderRadius: '8px',
            border: '1px solid rgba(255, 255, 255, 0.15)',
            background: news.category === 'news' ? 'rgba(248, 176, 64, 0.1)' : news.category === 'update' ? 'rgba(9, 195, 39, 0.1)' : 'rgba(51, 160, 239, 0.1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            gap: '8px',
          }}
        >
          <div
            style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              background: news.category === 'news' ? 'rgba(248, 176, 64, 1)' : news.category === 'update' ? 'rgba(9, 195, 39, 1)' : 'rgba(51, 160, 239, 1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '16px',
            }}
          >
            {news.category === 'news' ? '📰' : news.category === 'update' ? '🔄' : '📢'}
          </div>
          <span
            style={{
              color: news.category === 'news' ? 'rgba(248, 176, 64, 1)' : news.category === 'update' ? 'rgba(9, 195, 39, 1)' : 'rgba(51, 160, 239, 1)',
              fontSize: '12px',
              fontWeight: '600',
              textTransform: 'uppercase',
              letterSpacing: '1px',
            }}
          >
            {news.category === 'news' ? 'NEWS' : news.category === 'update' ? 'PRODUCTS' : 'COOPERATE'}
          </span>
        </div>

        <Typography.Title
          level={3}
          style={{
            color: '#ffffff',
            marginBottom: '16px',
            fontSize: '20px',
            lineHeight: '1.4',
          }}
        >
          {news.title}
        </Typography.Title>

        <Typography.Paragraph
          style={{
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '14px',
            lineHeight: '1.6',
            marginBottom: '16px',
          }}
        >
          {news.content.length > 120 ? `${news.content.substring(0, 120)}...` : news.content}
        </Typography.Paragraph>

        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span
            style={{
              color: 'rgba(255, 255, 255, 0.6)',
              fontSize: '12px',
            }}
          >
            {news.author}
          </span>
          <span
            style={{
              color: 'rgba(255, 255, 255, 0.5)',
              fontSize: '11px',
            }}
          >
            {new Date(news.publishedAt).toLocaleDateString()}
          </span>
        </div>
      </Card>
    </motion.div>
  )

  // 加载状态
  if (loading) {
    return (
      <div
        style={{
          width: '100%',
          background: 'transparent',
        }}
      >
        {/* Scrolling Text Banner */}
        <div className="scrolling-banner-container">
          <div className="scrolling-banner">
            <span className="scrolling-text">
              WEB3 <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF <span className="dot-separator">•</span> SCIENCE <span className="dot-separator">•</span> WEB3 <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF{' '}
              <span className="dot-separator">•</span> SCIENCE <span className="dot-separator">•</span> WEB3 <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF <span className="dot-separator">•</span> SCIENCE <span className="dot-separator">•</span> WEB3{' '}
              <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF <span className="dot-separator">•</span> SCIENCE
            </span>
          </div>
        </div>

        {/* Loading State */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: '40px',
            minHeight: '280px',
          }}
        >
          <Spin size="large" />
        </div>
      </div>
    )
  }

  // 错误状态
  if (error || newsItems.length === 0) {
    return (
      <div
        style={{
          width: '100%',
          background: 'transparent',
        }}
      >
        {/* Scrolling Text Banner */}
        <div className="scrolling-banner-container">
          <div className="scrolling-banner">
            <span className="scrolling-text">
              WEB3 <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF <span className="dot-separator">•</span> SCIENCE <span className="dot-separator">•</span> WEB3 <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF{' '}
              <span className="dot-separator">•</span> SCIENCE <span className="dot-separator">•</span> WEB3 <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF <span className="dot-separator">•</span> SCIENCE <span className="dot-separator">•</span> WEB3{' '}
              <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF <span className="dot-separator">•</span> SCIENCE
            </span>
          </div>
        </div>

        {/* Error State */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: '40px',
            minHeight: '280px',
            color: 'rgba(255, 255, 255, 0.6)',
          }}
        >
          <Typography.Text style={{ color: 'rgba(255, 255, 255, 0.6)' }}>Failed to load news. Please try again later.</Typography.Text>
        </div>
      </div>
    )
  }

  return (
    <div
      className="news-panel-container"
      style={{
        width: '100%',
        background: 'transparent',
      }}
    >
      {/* Scrolling Text Banner */}
      <div className="scrolling-banner-container">
        <div className="scrolling-banner">
          <span className="scrolling-text">
            WEB3 <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF <span className="dot-separator">•</span> SCIENCE <span className="dot-separator">•</span> WEB3 <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF{' '}
            <span className="dot-separator">•</span> SCIENCE <span className="dot-separator">•</span> WEB3 <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF <span className="dot-separator">•</span> SCIENCE <span className="dot-separator">•</span> WEB3{' '}
            <span className="dot-separator">•</span> BRAIN <span className="dot-separator">•</span> OF <span className="dot-separator">•</span> SCIENCE
          </span>
        </div>
      </div>

      {/* News Cards Section */}
      <div
        className="news-cards-container"
        style={{
          marginTop: '40px',
        }}
      >
        {/* Left Arrow */}
        <motion.div
          className="news-arrow"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={goToPrevious}
          style={{
            cursor: 'pointer',
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: '24px',
            padding: '10px',
          }}
        >
          <LeftOutlined />
        </motion.div>

        {/* Dynamic News Cards */}
        {currentNews.map((news, index) => renderNewsCard(news, index))}

        {/* Right Arrow */}
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={goToNext}
          style={{
            cursor: 'pointer',
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: '24px',
            padding: '10px',
          }}
        >
          <RightOutlined />
        </motion.div>
      </div>
    </div>
  )
}
