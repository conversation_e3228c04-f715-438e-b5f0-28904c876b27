/* Scrolling Banner Styles */
.scrolling-banner-container {
  width: 100%;
  height: 60px;
  overflow: hidden;
  display: flex;
  align-items: center;
  position: relative;
}

.scrolling-banner {
  display: flex;
  align-items: center;
  height: 100%;
  animation: scroll-left 30s linear infinite;
  white-space: nowrap;
}

.scrolling-text {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 3px;
  text-transform: uppercase;
}

.dot-separator {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin: 0 8px;
  font-weight: 400;
}

@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .scrolling-banner-container {
    height: 50px;
  }

  .scrolling-text {
    font-size: 16px;
    letter-spacing: 2px;
  }

  .dot-separator {
    font-size: 12px;
    margin: 0 6px;
  }

  .scrolling-banner {
    animation: scroll-left 20s linear infinite;
  }

  /* News Cards Responsive */
  .news-cards-container {
    flex-direction: column !important;
    gap: 16px !important;
  }

  .news-card {
    width: 100% !important;
    max-width: 500px !important;
  }

  .news-arrow {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .scrolling-banner-container {
    height: 40px;
  }

  .scrolling-text {
    font-size: 14px;
    letter-spacing: 1px;
  }

  .dot-separator {
    font-size: 10px;
    margin: 0 4px;
  }

  .scrolling-banner {
    animation: scroll-left 15s linear infinite;
  }

  .news-card {
    min-height: 180px !important;
  }

  .news-card-title {
    font-size: 18px !important;
  }

  .news-card-content {
    font-size: 13px !important;
  }
}

/* Tablet specific styles */
@media (min-width: 481px) and (max-width: 768px) {
  .news-cards-container {
    flex-direction: column !important;
  }

  .news-card {
    width: 100% !important;
    max-width: 600px !important;
  }
}

/* Desktop styles */
@media (min-width: 769px) {
  .news-cards-container {
    flex-direction: row !important;
    justify-content: center !important;
  }

  .news-card {
    width: 400px !important;
  }
}

/* News Card Hover Effects */
.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25) !important;
  border-color: rgba(255, 255, 255, 0.25) !important;
}

/* Hover effects for better interactivity */
.scrolling-banner-container:hover .scrolling-banner {
  animation-play-state: paused;
}

/* Auto-play indicator styles */
.news-panel-container {
  position: relative;
}

.news-cards-container {
  position: relative;
}

/* Arrow styles for auto-play */
.news-arrow {
  transition: all 0.3s ease;
  opacity: 0.6;
}

.news-arrow:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Smooth transitions for card changes */
.news-card {
  transition: all 0.6s ease;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .news-card {
    transition: none;
  }

  .news-arrow {
    transition: none;
  }
}


/* Grid layout override for News cards */
.news-cards-container {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: 20px !important;
  align-items: stretch !important;
  justify-items: stretch !important;
}

.news-card { width: 100% !important; }

/* 箭头显示逻辑：<768 隐藏，>=768 显示 */
.news-arrow { display: none !important; }
@media (min-width: 768px) {
  .news-arrow { display: inline-flex !important; }
}

/* 列数断点（优化）
   - < 768px: 1列
   - 768–1023px: 2列
   - 1024–1439px: 3列
   - >= 1440px: 4列
*/
@media (max-width: 767px) {
  .news-cards-container { grid-template-columns: 1fr !important; }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .news-cards-container { grid-template-columns: repeat(2, 1fr) !important; }
}
@media (min-width: 1024px) and (max-width: 1439px) {
  .news-cards-container { grid-template-columns: repeat(3, 1fr) !important; }
}
@media (min-width: 1440px) {
  .news-cards-container { grid-template-columns: repeat(4, 1fr) !important; }
}
