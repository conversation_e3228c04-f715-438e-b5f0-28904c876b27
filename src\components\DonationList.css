/* Donation Table Styles */
.donation-table .ant-table {
  background: transparent !important;
}

.donation-table .ant-table-thead > tr > th {
  background: rgba(255, 255, 255, 0.1) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 12px;
}

.donation-table .ant-table-tbody > tr > td {
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.donation-table .ant-table-tbody > tr:hover > td {
  background: rgba(255, 255, 255, 0.05) !important;
}

/* Load More Button Styles */
.donation-load-more {
  background: rgba(64, 196, 255, 0.1) !important;
  border: 1px solid rgba(64, 196, 255, 0.3) !important;
  color: #40c4ff !important;
  transition: all 0.3s ease !important;
}

.donation-load-more:hover {
  background: rgba(64, 196, 255, 0.2) !important;
  border-color: #40c4ff !important;
  color: #ffffff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 196, 255, 0.3) !important;
}

.donation-load-more:focus {
  background: rgba(64, 196, 255, 0.2) !important;
  border-color: #40c4ff !important;
  color: #ffffff !important;
}

.donation-load-more .anticon {
  transition: transform 0.3s ease;
}

.donation-load-more:hover .anticon {
  transform: translateY(2px);
}

/* Loading spinner */
.donation-table .ant-spin-dot-item {
  background-color: #40c4ff !important;
}

/* Empty state */
.donation-table .ant-empty-description {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Responsive Design */
@media (max-width: 480px) {
  .donation-list-container {
    padding: 20px 10px !important;
  }

  .donation-list-title {
    font-size: 24px !important;
    margin-bottom: 24px !important;
  }

  .donation-search {
    max-width: 100% !important;
  }

  .donation-table .ant-table-thead > tr > th {
    font-size: 10px !important;
    padding: 8px 4px !important;
  }

  .donation-table .ant-table-tbody > tr > td {
    font-size: 12px !important;
    padding: 8px 4px !important;
  }

  .donation-load-more {
    width: 100% !important;
    max-width: 200px !important;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .donation-list-container {
    padding: 30px 15px !important;
  }

  .donation-list-title {
    font-size: 28px !important;
    margin-bottom: 32px !important;
  }

  .donation-search {
    max-width: 100% !important;
  }

  .donation-table .ant-table-thead > tr > th {
    font-size: 11px !important;
  }

  .donation-table .ant-table-tbody > tr > td {
    font-size: 13px !important;
  }
}

@media (min-width: 769px) {
  .donation-list-container {
    padding: 40px 20px !important;
  }

  .donation-search {
    width: 100% !important;
  }
}
