.unified-footer {
  padding: 12px 0;
  margin-top: auto;
  width: 100%;
  transition: all 0.3s ease;
  z-index: 1;
  background: #1a2031;
  color: #fff;
}

.footer-container {
  width: 90%;
  margin: 0 auto;
  padding: 0 16px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-logo {
  width: 48px;
  height: 48px;
  object-fit: contain;
  border-radius: 50%;
}

.brand-info {
  display: flex;
  flex-direction: column;
  color: #fff;
}

.brand-name {
  font-size: 24px;
  font-weight: bold;
}

.light-theme .brand-name {
  color: #fff;
}

.dark-theme .brand-name {
  color: white;
}

.brand-tagline {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.light-theme .brand-tagline {
  color: #fff;
}

.dark-theme .brand-tagline {
  color: #ccc;
}

.footer-social {
  display: flex;
  align-items: center;
  gap: 16px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  /* 默认状态：深色背景，白色图标 */
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.social-link:hover {
  /* 悬停状态：白色背景，深色图标 */
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
  border: 2px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px) scale(1.05);
}

.social-icon {
  width: 20px;
  height: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .unified-footer {
    padding: 16px 0;
    background: rgba(255, 255, 255, 0.9);
    border-top: 2px solid rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
  }

  .footer-container {
    width: 95%;
    padding: 0 20px;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .footer-brand {
    flex-direction: column;
    gap: 12px;
  }

  .brand-name {
    color: #333;
    font-size: 20px;
  }

  .brand-tagline {
    text-align: center;
    color: #666;
  }

  .footer-social {
    justify-content: center;
    gap: 20px;
  }

  .social-link {
    width: 50px;
    height: 50px;
    /* 移动端保持相同的悬停效果 */
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .social-link:hover {
    background-color: rgba(255, 255, 255, 0.95);
    color: #333;
    border: 2px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px) scale(1.05);
  }

  .social-icon {
    width: 24px;
    height: 24px;
  }
}
