import { useEffect, useState } from "react";
import { Card, Col, Row, Statistic } from "antd";
import { PublicKey, Connection } from "@solana/web3.js";
import { useConnection } from "@solana/wallet-adapter-react";
import { motion } from "framer-motion";
import { getAssociatedTokenAddressSync, getAccount } from "@solana/spl-token";
import { Buffer as BufferPolyfill } from "buffer";
import { WalletOutlined, FileTextOutlined, ApiOutlined, ClockCircleOutlined } from "@ant-design/icons";
import "./StatsPanel.css";

// Polyfill Buffer for browser environments
globalThis.Buffer = BufferPolyfill;

// Program and token constants
const TOKEN_MINT = new PublicKey("GxdTh6udNstGmLLk9ztBb6bkrms7oLbrJp5yzUaVpump");
const FOUNDATION_1 = new PublicKey("9pjiPKiyDLHXRRGUu4udCceF3vj3EY6hZH3jdS9vEbkH");
// Countdown target date
const TARGET_DATE = new Date("2025-06-25T00:00:00");

// Define types for Irys GraphQL response
interface Tag {
  name: string;
  value: string;
}

interface Node {
  id: string;
  tags: Tag[];
}

interface Edge {
  node: Node;
  cursor: string;
}

interface Transactions {
  edges: Edge[];
}

interface GraphQLResponse {
  data?: {
    transactions?: Transactions;
  };
}

// Statistics JSON structure
interface StatisticsData {
  root?: {
    latestCursor: string;
    totalCount: number;
  };
  latestCursor?: string;
  totalCount?: number;
}

// LocalStorage keys for caching
const IRYS_CACHE_KEY = "irys_stats_cache";
const IRYS_TIMESTAMP_KEY = "irys_last_update";

// 硬编码的起始检查点（定期更新）
const HARDCODED_CHECKPOINT = {
  cursor: "Mm95S2FUSzFKNEhwMVV6ZUtQNlRneVU3MWFNbVROcGFrWTg4QmhTaHhveFk", // 2024年12月最新点
  count: 252000, // 对应的大概数量（略低于实际值，确保安全）
  timestamp: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1天前
  description: "2024-12-28 checkpoint (252k DOIs)",
};

interface IrysCacheData {
  totalCount: number; // 总数量
  lastCursor: string | null;
  lastUpdate: number;
  checkpoints: Array<{
    // 检查点数组
    cursor: string;
    count: number;
    timestamp: number;
  }>;
}

export const StatsPanel: React.FC = () => {
  const { connection } = useConnection();
  const [stats, setStats] = useState({
    foundation1Balance: 0,
    researchPapersUploaded: 0,
    scaiEngineStatus: "Offline" as "Online" | "Offline",
  });
  const [countdown, setCountdown] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Cache management functions
  const getCachedData = (): IrysCacheData | null => {
    try {
      const cached = localStorage.getItem(IRYS_CACHE_KEY);
      if (!cached) return null;

      const parsed = JSON.parse(cached);

      // Validate cache data structure
      if (typeof parsed.totalCount !== "number" || typeof parsed.lastUpdate !== "number" || (parsed.lastCursor !== null && typeof parsed.lastCursor !== "string")) {
        console.warn("Invalid cache data structure, clearing cache");
        localStorage.removeItem(IRYS_CACHE_KEY);
        return null;
      }

      return parsed;
    } catch (error) {
      console.warn("Failed to parse cached Irys data:", error);
      localStorage.removeItem(IRYS_CACHE_KEY);
      return null;
    }
  };

  const setCachedData = (totalCount: number, lastCursor: string | null, checkpoints: Array<{ cursor: string; count: number; timestamp: number }> = []): void => {
    try {
      console.log(`📊 setCachedData called with: totalCount=${totalCount} (type: ${typeof totalCount}), cursor=${lastCursor?.substring(0, 20)}...`);

      // 获取现有检查点
      const existingData = getCachedData();
      const existingCheckpoints = existingData?.checkpoints || [];

      // 合并检查点
      const allCheckpoints = [...existingCheckpoints, ...checkpoints];

      const data: IrysCacheData = {
        totalCount,
        lastCursor,
        lastUpdate: Date.now(),
        checkpoints: allCheckpoints,
      };
      console.log(`📊 Data to cache:`, data);
      localStorage.setItem(IRYS_CACHE_KEY, JSON.stringify(data));
      console.log(`📊 Successfully cached data with ${allCheckpoints.length} checkpoints`);
    } catch (error) {
      console.warn("Failed to cache Irys data:", error);
    }
  };

  const shouldRefreshCache = (cachedData: IrysCacheData | null): boolean => {
    if (!cachedData) return true;

    // Refresh if cache is older than 5 minutes (more frequent checks)
    const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
    const now = Date.now();
    return now - cachedData.lastUpdate > CACHE_DURATION;
  };

  const clearCache = (): void => {
    try {
      localStorage.removeItem(IRYS_CACHE_KEY);
      console.log("📊 Irys cache cleared - will do full rescan on next fetch");
    } catch (error) {
      console.warn("Failed to clear Irys cache:", error);
    }
  };

  // Expose clearCache function to global scope for debugging
  useEffect(() => {
    (window as any).clearIrysCache = clearCache;

    // Auto-clear cache if it seems stuck (temporary fix)
    try {
      const cachedData = getCachedData();
      if (cachedData) {
        console.log(`📊 Current cache: totalCount=${cachedData.totalCount} (type: ${typeof cachedData.totalCount})`);

        // Clear cache if totalCount is not a number or is too low
        if (typeof cachedData.totalCount !== "number" || cachedData.totalCount < 150000) {
          console.log(`📊 Cache invalid or outdated, clearing automatically...`);
          clearCache();
        }
      }
    } catch (error) {
      console.log("📊 Cache error, clearing...");
      clearCache();
    }

    return () => {
      delete (window as any).clearIrysCache;
    };
  }, []);

  // Countdown logic
  const updateCountdown = () => {
    const now = new Date();
    const timeDiff = TARGET_DATE.getTime() - now.getTime();
    if (timeDiff <= 0) {
      setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      return;
    }
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
    setCountdown({ days, hours, minutes, seconds });
  };

  // 1️⃣ 获取最新的statistics记录（里面存有上次cursor和累计总数）
  const getLatestStatisticsId = async (): Promise<string | null> => {
    const query = `
            query {
                transactions(
                    tags: [
                        { name: "App-Name", values: ["scivault"] },
                        { name: "Content-Type", values: ["application/json"] },
                        { name: "Version", values: ["2.0.0"] },
                        { name: "type", values: ["statistics"] }
                    ],
                    first: 1,
                    order: DESC
                ) {
                    edges {
                        node {
                            id
                        }
                    }
                }
            }
        `;
    try {
      console.log("🔎 查询最新统计记录...");
      const res = await fetch("https://uploader.irys.xyz/graphql", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ query }),
      });
      const json = await res.json();
      const edges = json?.data?.transactions?.edges || [];
      if (!edges.length) {
        console.log("❌ 没有找到任何历史统计记录");
        return null;
      }
      return edges[0].node.id;
    } catch (error: any) {
      console.error("❌ 获取统计信息失败:", error.message);
      return null;
    }
  };

  // 2️⃣ 解析statistics JSON文件，拿到latestCursor和累计数量
  const getStatsDataById = async (id: string): Promise<{ latestCursor: string; totalCount: number } | null> => {
    try {
      console.log(`⬇️  下载statistics数据文件: ${id}`);
      const res = await fetch(`https://gateway.irys.xyz/${id}`);
      const json: StatisticsData = await res.json();
      const latestCursor = json.root?.latestCursor || json.latestCursor;
      const totalCount = json.root?.totalCount || json.totalCount || 0;

      if (!latestCursor) {
        console.log("❌ JSON文件中缺少latestCursor字段");
        return null;
      }

      console.log(`✅ 获取到 latestCursor: ${latestCursor.slice(0, 8)}...，累计总数: ${totalCount}`);
      return { latestCursor, totalCount };
    } catch (error: any) {
      console.error("❌ 下载或解析statistics文件失败:", error.message);
      return null;
    }
  };

  // 3️⃣ 从cursor开始增量分页统计
  const fetchNewPdfsFromCursor = async (startCursor: string): Promise<{ newDois: number; newFiles: number }> => {
    console.log("📊 从上次cursor开始查询增量PDF交易...");
    let allDois = new Set<string>();
    let cursor = startCursor;
    let pageCount = 0;
    let totalFiles = 0;

    while (pageCount < 10000) {
      pageCount++;
      console.log(`\n📖 分页查询 - 第 ${pageCount} 页`);
      const query = `
                query {
                    transactions(
                        tags: [
                            { name: "App-Name", values: ["scivault"] },
                            { name: "Content-Type", values: ["application/pdf"] },
                            { name: "Version", values: ["2.0.0"] }
                        ],
                        first: 1000,
                        order: DESC
                        ${cursor ? `, after: "${cursor}"` : ""}
                    ) {
                        edges {
                            node {
                                id
                                tags { name value }
                            }
                            cursor
                        }
                    }
                }
            `;

      try {
        const res = await fetch("https://uploader.irys.xyz/graphql", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ query }),
        });
        const json: GraphQLResponse = await res.json();
        const edges = json?.data?.transactions?.edges || [];

        if (!edges.length) {
          console.log("✅ 没有更多新交易");
          break;
        }

        for (const edge of edges) {
          const doi = edge.node.tags.find((tag) => tag.name === "doi")?.value;
          if (doi) allDois.add(doi);
        }

        totalFiles += edges.length;
        console.log(`📄 本页文件数: ${edges.length}，累计唯一DOI: ${allDois.size}`);

        // 分页条件
        if (edges.length < 100) {
          console.log("✅ 本页不足100条，已到最后一页");
          break;
        }
        cursor = edges[edges.length - 1]?.cursor;
        if (!cursor) {
          console.log("✅ 没有cursor字段，已到最后一页");
          break;
        }
      } catch (err: any) {
        console.error("❌ GraphQL查询出错:", err.message);
        break;
      }
    }

    console.log(`\n🎉 增量查询结束`);
    console.log(`✅ 新增唯一DOI数: ${allDois.size}`);
    console.log(`✅ 新增文件数: ${totalFiles}`);
    return { newDois: allDois.size, newFiles: totalFiles };
  };

  // Check for new data since last cache
  const checkForNewData = async (lastCursor: string | null): Promise<boolean> => {
    if (!lastCursor) return true; // No cursor means we need full scan

    console.log("🔍 Checking for new data since last scan...");

    const query = `
            query {
                transactions(
                    tags: [
                        { name: "App-Name", values: ["scivault"] },
                        { name: "Content-Type", values: ["application/pdf"] },
                        { name: "Version", values: ["2.0.0"] }
                    ],
                    first: 100,
                    order: DESC
                ) {
                    edges {
                        cursor
                    }
                }
            }
        `;

    try {
      const response = await fetch("https://uploader.irys.xyz/graphql", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ query }),
      });

      const result: GraphQLResponse = await response.json();
      const edges = result.data?.transactions?.edges || [];

      if (edges.length === 0) return false;

      // Check if the latest cursor is different from our cached cursor
      const latestCursor = edges[0]?.cursor;
      const hasNewData = latestCursor !== lastCursor;

      console.log(`🔍 Latest cursor: ${latestCursor?.substring(0, 20)}...`);
      console.log(`🔍 Cached cursor: ${lastCursor?.substring(0, 20)}...`);
      console.log(`🔍 Has new data: ${hasNewData}`);

      return hasNewData;
    } catch (error) {
      console.warn("Failed to check for new data:", error);
      return true; // If check fails, assume we need to refresh
    }
  };

  // 获取最佳起始点（从检查点或硬编码点开始）
  const getBestStartingPoint = (cachedData: IrysCacheData | null): { cursor: string | null; count: number } => {
    // 1. 如果有缓存数据且较新，从缓存开始
    if (cachedData && cachedData.lastCursor) {
      const cacheAge = Date.now() - cachedData.lastUpdate;
      if (cacheAge < 24 * 60 * 60 * 1000) {
        // 24小时内
        console.log(`📍 Starting from cached position: ${cachedData.totalCount} DOIs`);
        return { cursor: cachedData.lastCursor, count: cachedData.totalCount };
      }
    }

    // 2. 检查是否有较新的检查点
    if (cachedData?.checkpoints && cachedData.checkpoints.length > 0) {
      const latestCheckpoint = cachedData.checkpoints[cachedData.checkpoints.length - 1];
      console.log(`📍 Starting from latest checkpoint: ${latestCheckpoint.count} DOIs`);
      return { cursor: latestCheckpoint.cursor, count: latestCheckpoint.count };
    }

    // 3. 使用硬编码的检查点
    console.log(`📍 Starting from hardcoded checkpoint: ${HARDCODED_CHECKPOINT.count} DOIs`);
    return { cursor: HARDCODED_CHECKPOINT.cursor, count: HARDCODED_CHECKPOINT.count };
  };

  // Optimized Irys PDF statistics with checkpoint-based incremental scanning
  const getIrysStats = async (): Promise<number> => {
    const cachedData = getCachedData();

    // If we have cached data, check if it's still fresh AND no new data
    if (cachedData && !shouldRefreshCache(cachedData)) {
      // Check for new data even if cache is fresh
      const hasNewData = await checkForNewData(cachedData.lastCursor);

      if (!hasNewData) {
        console.log("📊 Using cached Irys stats (no new data):", cachedData.totalCount);
        return cachedData.totalCount;
      } else {
        console.log("📊 New data detected, refreshing cache...");
      }
    }

    console.log("📊 Fetching Irys stats with checkpoint-based scanning...");

    // 获取最佳起始点
    const startingPoint = getBestStartingPoint(cachedData);
    let totalUniqueCount = startingPoint.count;
    let cursor: string | null = startingPoint.cursor;
    let pageCount = 0;
    const maxPages = 1000; // 扩展到1000页，确保完整扫描
    let consecutiveSmallPages = 0; // 连续小页面计数器
    const checkpoints: Array<{ cursor: string; count: number; timestamp: number }> = []; // 新检查点
    let lastCheckpointCount = totalUniqueCount; // 上次检查点的数量

    while (pageCount < maxPages) {
      pageCount++;

      const query = `
                query {
                    transactions(
                        tags: [
                            { name: "App-Name", values: ["scivault"] },
                            { name: "Content-Type", values: ["application/pdf"] },
                            { name: "Version", values: ["2.0.0"] }
                        ],
                        first: 1000,
                        order: DESC
                        ${cursor ? `, after: "${cursor}"` : ""}
                    ) {
                        edges {
                            node {
                                id
                                tags {
                                    name
                                    value
                                }
                            }
                            cursor
                        }
                    }
                }
            `;

      try {
        const response = await fetch("https://uploader.irys.xyz/graphql", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ query }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const result: GraphQLResponse = await response.json();
        const edges = result.data?.transactions?.edges || [];

        if (edges.length === 0) {
          console.log("📊 No more data available");
          break;
        }

        // Count unique DOIs in this batch only
        const batchDois = new Set<string>();
        edges.forEach((edge) => {
          const doi = edge.node.tags.find((tag) => tag.name === "doi")?.value;
          if (doi) {
            batchDois.add(doi);
          }
        });

        totalUniqueCount += batchDois.size;

        // 每1000条DOI保存一个检查点
        if (totalUniqueCount - lastCheckpointCount >= 1000 && cursor) {
          checkpoints.push({
            cursor: cursor,
            count: totalUniqueCount,
            timestamp: Date.now(),
          });
          lastCheckpointCount = totalUniqueCount;
          console.log(`💾 Checkpoint saved at ${totalUniqueCount} DOIs`);
        }

        // 显示详细进度，每50页显示一次汇总
        if (pageCount % 50 === 0) {
          console.log(`🔄 Progress: Page ${pageCount}/1000, Total DOIs: ${totalUniqueCount}, Checkpoints: ${checkpoints.length}`);
        }
        console.log(`📄 Page ${pageCount}: ${edges.length} files, ${batchDois.size} unique DOIs, running total: ${totalUniqueCount}`);

        // Track consecutive small pages
        if (edges.length < 500) {
          consecutiveSmallPages++;
          console.log(`⚠️  Small page detected (${edges.length} < 500), consecutive count: ${consecutiveSmallPages}`);
        } else {
          consecutiveSmallPages = 0; // Reset counter
        }

        // Update cursor for next iteration
        const lastCursor = edges[edges.length - 1]?.cursor;
        if (lastCursor) {
          cursor = lastCursor;
        } else {
          console.log("📊 No more cursor, reached end");
          break;
        }

        // Stop if we get multiple consecutive small pages (likely at end)
        if (consecutiveSmallPages >= 3) {
          console.log(`📊 ${consecutiveSmallPages} consecutive small pages, likely at end`);
          break;
        }

        // Also stop if we get a very small page AND no cursor
        if (edges.length < 10 && !lastCursor) {
          console.log(`📊 Very small page (${edges.length}) and no cursor, definitely at end`);
          break;
        }
      } catch (err) {
        console.warn("Irys fetch error:", err);
        // If we have cached data and fetch fails, return cached value
        if (cachedData) {
          console.log("📊 Fetch failed, using cached data:", cachedData.totalCount);
          return cachedData.totalCount;
        }
        throw err;
      }
    }

    // Check if we reached max pages
    if (pageCount >= maxPages) {
      console.warn(`⚠️  Reached maximum page limit (${maxPages}), there might be more data available`);
      console.warn(`📊 Consider increasing maxPages if needed`);
    }

    // Update cache with new data and checkpoints
    console.log(`📊 About to cache: totalCount=${totalUniqueCount}, cursor=${cursor?.substring(0, 20)}..., checkpoints=${checkpoints.length}`);
    setCachedData(totalUniqueCount, cursor, checkpoints);

    console.log(`📊 Irys stats updated: ${totalUniqueCount} total unique DOIs (scanned ${pageCount} pages, saved ${checkpoints.length} checkpoints)`);
    return totalUniqueCount;
  };

  // Check SCAI Engine status
  const checkScaiStatus = async (): Promise<"Online" | "Offline"> => {
    try {
      const response = await fetch("https://api.scai.sh/dois?page=1", {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });

      return response.ok ? "Online" : "Offline";
    } catch (err) {
      console.warn("SCAI status check error:", err);
      return "Offline";
    }
  };

  // Fetch stats (Foundation Balance, Irys, SCAI)
  const fetchStats = async (isInitialFetch: boolean) => {
    if (isInitialFetch) {
      setInitialLoading(true);
    }
    setError(null);
    try {
      if (!connection) {
        setError("Connection not ready.");
        if (isInitialFetch) {
          setInitialLoading(false);
        }
        return;
      }

      // Fetch foundation wallet token balance
      const foundation1ATA = getAssociatedTokenAddressSync(TOKEN_MINT, FOUNDATION_1);
      let foundation1Balance = 0;

      try {
        const foundation1Account = await getAccount(connection, foundation1ATA);
        foundation1Balance = Number(foundation1Account.amount) / 1_000_000;
      } catch (error: any) {
        console.warn(`Foundation token account not found at ${foundation1ATA.toBase58()}:`, error.message);
      }

      // Fetch Irys and SCAI stats
      let researchPapersUploaded = 0;
      let scaiEngineStatus: "Online" | "Offline" = "Offline";
      try {
        researchPapersUploaded = await getIrysStats();
      } catch (err) {
        console.warn("Failed to fetch Irys stats:", err);
      }

      try {
        scaiEngineStatus = await checkScaiStatus();
      } catch (err) {
        console.warn("Failed to fetch SCAI status:", err);
      }

      // Update stats
      setStats({
        foundation1Balance,
        researchPapersUploaded,
        scaiEngineStatus,
      });
    } catch (err: any) {
      console.error("Error fetching stats:", err);
      setError("Failed to fetch stats: " + err.message);
    } finally {
      if (isInitialFetch) {
        setInitialLoading(false);
      }
    }
  };

  useEffect(() => {
    // Initial fetch
    fetchStats(true);
    updateCountdown();

    // Polling every 15 seconds (silent) for stats
    const statsInterval = setInterval(() => fetchStats(false), 15000);
    // Update countdown every second
    const countdownInterval = setInterval(updateCountdown, 1000);

    return () => {
      clearInterval(statsInterval);
      clearInterval(countdownInterval);
    };
  }, [connection]);

  return (
    <motion.div
      className="stats-panel-container"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      style={{
        width: "100%",
        maxWidth: "1200px",
        margin: "0 auto",
        padding: "40px 20px",
      }}
    >
      {/* Title */}
      <div
        className="stats-panel-title"
        style={{
          color: "#ffffff",
          fontSize: "32px",
          fontWeight: "bold",
          textAlign: "center",
          letterSpacing: "2px",
          textTransform: "uppercase",
          marginBottom: "40px",
        }}
      >
        Foundation Stats
      </div>

      {/* Loading State */}
      {initialLoading && (
        <div style={{ textAlign: "center", padding: "50px" }}>
          <div style={{ color: "#ffffff", fontSize: "18px" }}>Loading statistics...</div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div style={{ textAlign: "center", padding: "50px" }}>
          <div style={{ color: "#ff4d4f", fontSize: "18px" }}>Error: {error}</div>
        </div>
      )}

      {/* Stats Grid */}
      {!initialLoading && !error && (
        <div
          className="stats-grid"
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(4, 1fr)",
            gap: "24px",
          }}
        >
          {/* Foundation Balance Card */}
          <Card
            style={{
              background: "rgba(255, 255, 255, 0.1)",
              backdropFilter: "blur(32px)",
              borderRadius: "16px",
              boxShadow: "0 8px 24px rgba(0, 0, 0, 0.2)",
              padding: "0",
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <div
                style={{
                  color: "#fff",
                  fontSize: "24px",
                  fontWeight: "bold",
                }}
              >
                {stats.foundation1Balance.toFixed(2)}
              </div>
              <div
                style={{
                  color: "#6f6f7b",
                  fontSize: "14px",
                  marginTop: "4px",
                }}
              >
                TOKENS
              </div>
            </div>

            <div
              style={{
                color: "#6f6f7b",
                fontSize: "14px",
                marginBottom: "12px",
                textTransform: "uppercase",
                letterSpacing: "1px",
              }}
            >
              Foundation Balance
            </div>
          </Card>

          {/* Research Papers Card */}
          <Card
            style={{
              background: "rgba(255, 255, 255, 0.1)",
              backdropFilter: "blur(32px)",
              borderRadius: "16px",
              boxShadow: "0 8px 24px rgba(0, 0, 0, 0.2)",
            }}
            bodyStyle={{ padding: "24px" }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <div
                style={{
                  color: "#fff",
                  fontSize: "24px",
                  fontWeight: "bold",
                }}
              >
                {stats.researchPapersUploaded.toLocaleString()}
              </div>
              <div
                style={{
                  color: "#6f6f7b",
                  fontSize: "14px",
                  marginTop: "4px",
                }}
              >
                Papers
              </div>
            </div>
            <div
              style={{
                color: "#6f6f7b",
                fontSize: "14px",
                marginBottom: "12px",
                textTransform: "uppercase",
                letterSpacing: "1px",
              }}
            >
              Research Papers Uploaded
            </div>
          </Card>

          {/* SCAI Engine Status Card */}
          <Card
            style={{
              background: "rgba(255, 255, 255, 0.1)",
              backdropFilter: "blur(32px)",
              borderRadius: "16px",
              boxShadow: "0 8px 24px rgba(0, 0, 0, 0.2)",
            }}
            bodyStyle={{ padding: "24px" }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <div
                style={{
                  color: stats.scaiEngineStatus === "Online" ? "#52c41a" : "#ff4d4f",
                  fontSize: "24px",
                  fontWeight: "bold",
                  display: "flex",
                  alignItems: "center",
                  gap: "12px",
                  textShadow: "0 1px 2px rgba(0, 0, 0, 0.3)",
                }}
              >
                {stats.scaiEngineStatus}
                <span style={{ fontSize: "20px" }}>{stats.scaiEngineStatus === "Online" ? <img className="stats-panel-online" src="/online.png" alt="Online" /> : <img className="stats-panel-offline" src="/offline.png" alt="Offline" />}</span>
              </div>
            </div>
            <div
              style={{
                color: "#6f6f7b",
                fontSize: "14px",
                marginBottom: "12px",
                textTransform: "uppercase",
                letterSpacing: "1px",
              }}
            >
              SCAI Engine Status
            </div>
          </Card>

          {/* Contributors Countdown Card */}
          <Card
            style={{
              background: "linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 20%)",
              backdropFilter: "blur(30px)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              borderRadius: "16px",
              boxShadow: "0 8px 24px rgba(0, 0, 0, 0.2)",
            }}
            bodyStyle={{ padding: "24px" }}
          >
            <div
              style={{
                color: "#fff",
                fontSize: "24px",
                fontWeight: "bold",
                fontFamily: "monospace",
                textShadow: "0 1px 2px rgba(0, 0, 0, 0.3)",
              }}
            >
              {String(countdown.days)} <span style={{ fontSize: "12px" }}>d</span> {String(countdown.hours)} <span style={{ fontSize: "12px" }}>h</span> {String(countdown.minutes)} <span style={{ fontSize: "12px" }}>m</span> {String(countdown.seconds)} <span style={{ fontSize: "12px" }}>s</span>
            </div>
            <div
              style={{
                color: "#6f6f7b",
                fontSize: "14px",
                marginBottom: "12px",
                textTransform: "uppercase",
                letterSpacing: "1px",
              }}
            >
              Countdown to June 25, 2025
            </div>
          </Card>
        </div>
      )}
    </motion.div>
  );
};
